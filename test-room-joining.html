<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Room Joining Test</title>
    <script src="/socket.io/socket.io.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 20px; border: 1px solid #ccc; border-radius: 5px; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        #logs { background: #f8f9fa; padding: 10px; border-radius: 3px; height: 300px; overflow-y: auto; font-family: monospace; font-size: 12px; }
        input { padding: 8px; margin: 5px; border: 1px solid #ccc; border-radius: 3px; }
        .log-entry { margin: 2px 0; }
        .log-info { color: #007bff; }
        .log-success { color: #28a745; }
        .log-warn { color: #ffc107; }
        .log-error { color: #dc3545; }
    </style>
</head>
<body>
    <div class="container">
        <h1>WebRTC Room Joining Test</h1>
        
        <div class="section">
            <h3>Room Controls</h3>
            <input type="text" id="roomId" placeholder="Room ID" value="TEST123">
            <button onclick="joinAsHost()">Join as Host</button>
            <button onclick="joinAsParticipant()">Join as Participant</button>
            <button onclick="disconnect()">Disconnect</button>
        </div>

        <div class="section">
            <h3>Status</h3>
            <p>Socket ID: <span id="socketId">Not connected</span></p>
            <p>Room: <span id="currentRoom">None</span></p>
            <p>Role: <span id="currentRole">None</span></p>
            <p>Peers: <span id="peerCount">0</span></p>
        </div>

        <div class="section">
            <h3>Logs</h3>
            <div id="logs"></div>
            <button onclick="clearLogs()">Clear Logs</button>
        </div>
    </div>

    <script>
        let socket = null;
        let currentRoom = null;
        let currentRole = null;
        let peers = {};

        function log(level, message) {
            const timestamp = new Date().toLocaleTimeString();
            const logDiv = document.getElementById('logs');
            const entry = document.createElement('div');
            entry.className = `log-entry log-${level}`;
            entry.textContent = `[${timestamp}] ${message}`;
            logDiv.appendChild(entry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${level.toUpperCase()}] ${message}`);
        }

        function updateStatus() {
            document.getElementById('socketId').textContent = socket ? socket.id : 'Not connected';
            document.getElementById('currentRoom').textContent = currentRoom || 'None';
            document.getElementById('currentRole').textContent = currentRole || 'None';
            document.getElementById('peerCount').textContent = Object.keys(peers).length;
        }

        function joinAsHost() {
            const roomId = document.getElementById('roomId').value;
            if (!roomId) {
                log('error', 'Please enter a room ID');
                return;
            }
            joinRoom(roomId, true);
        }

        function joinAsParticipant() {
            const roomId = document.getElementById('roomId').value;
            if (!roomId) {
                log('error', 'Please enter a room ID');
                return;
            }
            joinRoom(roomId, false);
        }

        function joinRoom(roomId, isHost) {
            if (socket) {
                log('warn', 'Disconnecting existing connection');
                socket.disconnect();
            }

            log('info', `Connecting to server...`);
            socket = io();
            currentRoom = roomId;
            currentRole = isHost ? 'host' : 'participant';
            peers = {};

            socket.on('connect', () => {
                log('success', `Connected with socket ID: ${socket.id}`);
                log('info', `Joining room ${roomId} as ${isHost ? 'host' : 'participant'}`);
                socket.emit('join-room', { roomId, isHost });
                updateStatus();
            });

            socket.on('user-joined', ({ peerId }) => {
                log('info', `User joined: ${peerId}`);
                peers[peerId] = { id: peerId };
                updateStatus();
            });

            socket.on('initiate-connection', ({ peerId }) => {
                log('info', `Received initiate-connection for peer: ${peerId}`);
                // In a real app, this would create a peer connection
            });

            socket.on('offer', ({ offer, from }) => {
                log('info', `Received offer from: ${from}`);
            });

            socket.on('answer', ({ answer, from }) => {
                log('info', `Received answer from: ${from}`);
            });

            socket.on('ice-candidate', ({ candidate, from }) => {
                log('info', `Received ICE candidate from: ${from}`);
            });

            socket.on('user-left', ({ peerId }) => {
                log('info', `User left: ${peerId}`);
                delete peers[peerId];
                updateStatus();
            });

            socket.on('error', ({ message }) => {
                log('error', `Server error: ${message}`);
            });

            socket.on('disconnect', () => {
                log('warn', 'Disconnected from server');
                currentRoom = null;
                currentRole = null;
                peers = {};
                updateStatus();
            });
        }

        function disconnect() {
            if (socket) {
                socket.disconnect();
                socket = null;
                currentRoom = null;
                currentRole = null;
                peers = {};
                updateStatus();
                log('info', 'Disconnected');
            }
        }

        function clearLogs() {
            document.getElementById('logs').innerHTML = '';
        }

        // Initialize status
        updateStatus();
    </script>
</body>
</html>
