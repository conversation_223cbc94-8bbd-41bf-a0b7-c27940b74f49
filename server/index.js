const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const ffmpeg = require('fluent-ffmpeg');
const { spawn } = require('child_process');
const path = require('path');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: ["http://localhost:3001", "https://poc-webrtc-679769757300.us-central1.run.app", " https://0618d252aa6e.ngrok-free.app"],
    methods: ["GET", "POST"]
  }
});

app.use(cors());
app.use(express.json());

// Serve static files from the dist directory
app.use(express.static(path.join(__dirname, '../dist')));

// Store room information
const rooms = new Map();

class Room {
  constructor(id) {
    this.id = id;
    this.host = null;
    this.participants = new Map();
    this.rtmpStream = null;
  }

  addUser(socketId, isHost = false) {
    if (isHost) {
      this.host = socketId;
    } else {
      this.participants.set(socketId, {
        id: socketId,
        joinedAt: Date.now()
      });
    }
  }

  removeUser(socketId) {
    if (this.host === socketId) {
      this.host = null;
      // Stop RTMP stream if host leaves
      this.stopRTMP();
    } else {
      this.participants.delete(socketId);
    }
  }

  getUsers() {
    const users = [];
    if (this.host) users.push(this.host);
    users.push(...this.participants.keys());
    return users;
  }

  isEmpty() {
    return !this.host && this.participants.size === 0;
  }

  startRTMP(rtmpUrl, streamKey) {
    if (this.rtmpStream) {
      this.stopRTMP();
    }

    console.log(`Starting RTMP stream for room ${this.id}`);
    
    // This is a placeholder for RTMP streaming
    // In a real implementation, you would:
    // 1. Receive the composite video stream from the host
    // 2. Use FFmpeg to convert it to RTMP
    // 3. Stream to the specified endpoint

    
    this.rtmpStream = {
      url: rtmpUrl,
      key: streamKey,
      startedAt: Date.now(),
      process: null // Would store the FFmpeg process
    };

    return true;
  }

  stopRTMP() {
    if (this.rtmpStream) {
      console.log(`Stopping RTMP stream for room ${this.id}`);
      
      if (this.rtmpStream.process) {
        this.rtmpStream.process.kill('SIGTERM');
      }
      
      this.rtmpStream = null;
      return true;
    }
    return false;
  }
}

io.on('connection', (socket) => {
  console.log('User connected:', socket.id);

  socket.on('join-room', ({ roomId, isHost }) => {
    console.log(`User ${socket.id} joining room ${roomId} as ${isHost ? 'host' : 'participant'}`);
    
    // Create room if it doesn't exist
    if (!rooms.has(roomId)) {
      rooms.set(roomId, new Room(roomId));
    }

    const room = rooms.get(roomId);
    
    // Check if room already has a host and user is trying to be host
    if (isHost && room.host) {
      socket.emit('error', { message: 'Room already has a host' });
      return;
    }

    // Join the room
    socket.join(roomId);
    room.addUser(socket.id, isHost);
    socket.roomId = roomId;
    socket.isHost = isHost;

    // Notify existing users about new user
    socket.to(roomId).emit('user-joined', { peerId: socket.id });

    // Send existing users to new user
    const existingUsers = room.getUsers().filter(id => id !== socket.id);
    existingUsers.forEach(userId => {
      socket.emit('user-joined', { peerId: userId });
    });

    // If the new user is a participant, tell existing users (hosts) to initiate connection
    if (!isHost && existingUsers.length > 0) {
      console.log(`Telling existing users to initiate connection to participant ${socket.id}`);
      existingUsers.forEach(userId => {
        socket.to(userId).emit('initiate-connection', { peerId: socket.id });
        console.log(`Sent initiate-connection to ${userId} for participant ${socket.id}`);
      });
    }

    console.log(`Room ${roomId} now has ${room.getUsers().length} users`);
  });

  socket.on('offer', ({ offer, to }) => {
    console.log(`Relaying offer from ${socket.id} to ${to}`);
    socket.to(to).emit('offer', {
      offer,
      from: socket.id
    });
  });

  socket.on('answer', ({ answer, to }) => {
    console.log(`Relaying answer from ${socket.id} to ${to}`);
    socket.to(to).emit('answer', {
      answer,
      from: socket.id
    });
  });

  socket.on('ice-candidate', ({ candidate, to }) => {
    console.log(`Relaying ICE candidate from ${socket.id} to ${to}`);
    socket.to(to).emit('ice-candidate', {
      candidate,
      from: socket.id
    });
  });

  socket.on('start-rtmp', ({ rtmpUrl, streamKey }) => {
    if (!socket.isHost || !socket.roomId) {
      socket.emit('error', { message: 'Only hosts can start RTMP streams' });
      return;
    }

    const room = rooms.get(socket.roomId);
    if (room) {
      const success = room.startRTMP(rtmpUrl, streamKey);
      socket.emit('rtmp-status', { 
        streaming: success,
        message: success ? 'RTMP stream started' : 'Failed to start RTMP stream'
      });
    }
  });

  socket.on('stop-rtmp', () => {
    if (!socket.isHost || !socket.roomId) {
      socket.emit('error', { message: 'Only hosts can stop RTMP streams' });
      return;
    }

    const room = rooms.get(socket.roomId);
    if (room) {
      const success = room.stopRTMP();
      socket.emit('rtmp-status', { 
        streaming: false,
        message: success ? 'RTMP stream stopped' : 'No active stream to stop'
      });
    }
  });

  socket.on('disconnect', () => {
    console.log('User disconnected:', socket.id);
    
    if (socket.roomId) {
      const room = rooms.get(socket.roomId);
      if (room) {
        room.removeUser(socket.id);
        
        // Notify other users
        socket.to(socket.roomId).emit('user-left', { peerId: socket.id });
        
        // Clean up empty rooms
        if (room.isEmpty()) {
          console.log(`Removing empty room ${socket.roomId}`);
          rooms.delete(socket.roomId);
        } else {
          console.log(`Room ${socket.roomId} now has ${room.getUsers().length} users`);
        }
      }
    }
  });
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    rooms: rooms.size,
    timestamp: new Date().toISOString()
  });
});

// Get room info
app.get('/rooms/:roomId', (req, res) => {
  const { roomId } = req.params;
  const room = rooms.get(roomId);

  if (!room) {
    return res.status(404).json({ error: 'Room not found' });
  }

  res.json({
    id: room.id,
    hasHost: !!room.host,
    participantCount: room.participants.size,
    isStreaming: !!room.rtmpStream
  });
});

// Catch-all handler: send back React's index.html file for client-side routing
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, '../dist/index.html'));
});

const PORT = process.env.PORT || 3001;
server.listen(PORT, () => {
  console.log(`🚀 WebRTC Signaling Server running on port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/health`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  server.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});